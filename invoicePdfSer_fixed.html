<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Invoice</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      /* Base colors */
      .text-black-text {
        color: #20363f;
      }
      .text-text-color {
        color: #090a1a;
      }
      .bg-disabled {
        background-color: rgba(0, 0, 0, 0.04);
      }

      /* Grays */
      .bg-grey-25 {
        background-color: #fcfcfd;
      }
      .bg-grey-50 {
        background-color: #f9fafb;
      }
      .border-grey-200 {
        border-color: #e4e7ec;
      }
      .text-grey-500 {
        color: #667085;
      }

      /* Primary */
      .bg-primary-25 {
        background-color: #f5f6ff;
      }
      .bg-primary-50 {
        background-color: #e3e5f6;
      }
      .bg-primary-500 {
        background-color: #2d3484;
      }
      .text-primary-900 {
        color: #090a1a;
      }
      .text-primary-300 {
        color: #767bb2;
      }
      .border-primary-50 {
        border-color: #e3e5f6;
      }

      /* Secondary */
      .text-secondary-600 {
        color: #ef5c26;
      }

      /* Success */
      .bg-success-50 {
        background-color: #ecfdf3;
      }
      .text-success-600 {
        color: #039855;
      }

      /* Error */
      .bg-error-50 {
        background-color: #fef3f2;
      }
      .text-error-600 {
        color: #d92d20;
      }

      /* Warning */
      .bg-warning-50 {
        background-color: #fffaeb;
      }
      .text-warning-600 {
        color: #dc6803;
      }

      /* Custom font */
      .font-inter {
        font-family: Inter, sans-serif;
      }

      /* Table styling */
      .template-table th {
        border-bottom: 1px solid #e3e5f6;
        padding: 12px 16px;
        color: #090a1a;
        font-weight: 600;
        text-align: left;
      }
      .template-table th:not(:last-child) {
        border-right: 1px solid #e3e5f6;
      }
      .template-table td {
        padding: 4px 16px;
      }
      .template-table td:not(:last-child) {
        border-right: 1px solid #e3e5f6;
      }
      .template-table tbody tr:nth-child(even) {
        background-color: #f5f6ff;
      }

      .error-chip {
        color: rgb(220, 38, 38); /* text-error-600 */
        border: 1px solid rgb(220, 38, 38); /* border-error-600 */
        background-color: rgb(254, 242, 242); /* bg-error-25 */
        padding: 2px 8px; /* px-2 py-[2px] */
        border-radius: 0.375rem; /* rounded-md */
        line-height: 1.25rem; /* leading-5 */
        text-align: center;
      }

      .warning-chip {
        color: rgb(202, 138, 4); /* text-warning-600 */
        border: 1px solid rgb(202, 138, 4); /* border-warning-600 */
        background-color: rgb(254, 252, 232); /* bg-warning-25 */
        padding: 2px 8px;
        border-radius: 0.375rem;
        line-height: 1.25rem;
        text-align: center;
      }

      .success-chip {
        color: rgb(22, 163, 74); /* text-success-600 */
        border: 1px solid rgb(22, 163, 74);
        background-color: rgb(240, 253, 244); /* bg-success-25 */
        padding: 2px 8px;
        border-radius: 0.375rem;
        line-height: 1.25rem;
        text-align: center;
      }

      .grey-chip {
        color: rgb(75, 85, 99); /* text-grey-600 */
        border: 1px solid rgb(75, 85, 99);
        background-color: rgb(249, 250, 251); /* bg-grey-25 */
        padding: 2px 8px;
        border-radius: 0.375rem;
        line-height: 1.25rem;
        text-align: center;
      }

      .primary-chip {
        color: rgb(37, 99, 235); /* text-primary-600 */
        border: 1px solid rgb(37, 99, 235);
        background-color: rgb(239, 246, 255); /* bg-primary-25 */
        padding: 2px 8px;
        border-radius: 0.375rem;
        line-height: 1.25rem;
        text-align: center;
      }
    </style>
  </head>
  <body class="bg-white min-h-screen text-primary-900 w-full">
    ${ TEMPLATE_VARIABLE.INVOICE_SHADDER_IMAGE ? `<img
      src="${TEMPLATE_VARIABLE.INVOICE_SHADDER_IMAGE}"
      alt="Invoice Background"
      class="absolute left-0 top-0 mix-blend-multiply z-10"
    />` : '' }
    <div class="max-w-4xl mx-auto rounded-lg overflow-hidden relative p-[20px]">
      <div class="flex justify-between items-end mb-6 gap-4 relative z-50">
        <div>
          <img
            src="${TEMPLATE_VARIABLE.TENANT_LOGO}"
            alt="Lumigo Logo"
            class="h-8 mb-2"
          />
          <p class="text-xs leading-5">
            Lumigo Solution 2025 Inc (Lumigo Transport) <br />
            Phone: ************ Website: www.lumigotransport.ca
          </p>
        </div>
        <div class="text-left pl-5 border-l border-l-gary-300">
          <div class="text-sm text-primary-200">Invoice number:</div>
          <div class="font-semibold text-sm mb-2">INV000027</div>
          <div class="text-sm text-primary-200">Issued:</div>
          <div class="font-semibold text-sm">June 26, 2024</div>
        </div>
      </div>

      <div
        class="flex gap-2.5 bg-white text-sm border border-primary-50 p-6 rounded-lg relative z-50"
      >
        <!-- BILL TO -->
        <div>
          <h3 class="font-semibold mb-2 text-xs flex gap-2 items-center">
            ${rightSVG} BILL TO:
          </h3>
          <div
            class="bg-primary-25 border border-primary-50 rounded-lg p-6 text-primary-300 text-xs h-[calc(100%-24px)]"
          >
            <p
              class="font-medium text-gray-800 mb-4 text-sm flex gap-1 items-center"
            >
              ${userPrimarySVG} Mauro Sicard
            </p>
            <p class="mb-2">(612) 865 - 0989</p>
            <p class="mb-2"><EMAIL></p>
            <p>Palo Alto, San Francisco, CA 92102, United States of America</p>
          </div>
        </div>

        <!-- BILL FROM -->
        <div>
          <h3 class="font-semibold mb-2 text-xs flex gap-2 items-center">
            ${rightSVG}BILL FROM:
          </h3>
          <div
            class="bg-primary-25 border border-primary-50 rounded-lg p-6 text-primary-300 text-xs h-[calc(100%-24px)]"
          >
            <p
              class="font-medium text-gray-800 mb-4 text-sm flex gap-1 items-center"
            >
              ${buildingPrimarySVG} BRIX Agency
            </p>
            <p class="mb-2">(684) 879 - 0102</p>
            <p class="mb-2">
              Palo Alto, San Francisco, CA 94110, United States of America
            </p>
            <p class="mb-2"></p>
            <p>12345 6789 US0001</p>
          </div>
        </div>

        <!-- AMOUNT DUE -->
        <div class="h-full">
          <h3 class="font-semibold mb-2 text-xs flex gap-2 items-center">
            ${rightSVG} AMOUNT DUE:
          </h3>
          <div
            class="border border-primary-50 rounded-lg p-6 px-7 bg-primary-500 text-primary-300 h-[calc(100%-22px)]"
          >
            <p class="text-sm text-white mb-1">CAD</p>
            <p class="text-2xl font-bold text-white mb-2">$19,570.00</p>
            <p class="text-sm text-white mb-2">July 26, 2024</p>
            <span
              class="block w-fit text-green-600 font-semibold success-chip !px-2 !py-1"
            >
              Paid
            </span>
          </div>
        </div>
      </div>

      <!-- TABLE -->
      <div class="mt-4 overflow-auto border border-primary-50 rounded-lg">
        <table
          class="template-table min-w-full text-sm text-left !text-primary-900"
        >
          <thead>
            <tr class="bg-primary-25">
              <th>Tracking Number</th>
              <th>Service Level</th>
              <th>Completed Date</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            ${TEMPLATE_VARIABLE.INVOICE_ORDERS}
          </tbody>
        </table>
      </div>

      <!-- SUMMARY -->
      <div class="grid grid-cols-2 gap-3 mt-4 text-sm relative z-50">
        <div
          class="bg-primary-25 border border-primary-50 p-4 rounded-lg min-h-24"
        >
          <div class="flex justify-between items-center">
            <p class="font-medium">Total Orders</p>
            <p class="font-bold">16</p>
          </div>
          <div class="flex justify-between mt-1 items-center">
            <p class="font-medium mt-2">Balance</p>
            <p class="font-bold">$0.00</p>
          </div>
        </div>
        <div
          class="bg-primary-25 border border-primary-50 p-4 rounded-lg min-h-24"
        >
          <p class="flex justify-between items-center">
            Sub total <span class="font-bold">$100.00</span>
          </p>
          <p class="flex justify-between items-center mt-2">
            GST <span class="font-bold">$3.00</span>
          </p>
          <p class="flex justify-between items-center mt-2">
            QST <span class="font-bold">$9.00</span>
          </p>
          <p class="flex justify-between items-center mt-2">
            Total amount <span class="font-bold">$112.00</span>
          </p>
        </div>
      </div>

      <!-- TERMS -->
      <div class="mt-6 text-xs text-primary-900 leading-[18px]">
        <p>
          <span class="font-bold text-sm">Terms & Conditions:</span>
        </p>
        <p>
          Fees and payment terms will be established in the contract or
          agreement prior to the commencement of the project. An initial deposit
          will be required before any design work begins. We reserve the right
          to suspend or halt work in the event of non-payment.
        </p>
      </div>

      <div
        class="mt-6 text-primary-900 leading-[18px] rounded-lg overflow-hidden border border-[#BFC2DF] text-sm leading-[20px]"
      >
        <div class="bg-[#F5F6FF]">
          <div class="flex items-center px-4 py-2">
            <div class="flex-1">
              <span class="font-bold">Tracking Number:</span>
              ${TEMPLATE_VARIABLE.TRACKING_NUMBER}
            </div>
            <div class="flex-1">
              <span class="font-bold">Order Date:</span>
              ${TEMPLATE_VARIABLE.SUBMITTED_DATE}
            </div>
          </div>
          <div class="flex gap-4 border-y border-[#E3E5F6] px-4 py-2">
            <div class="flex-1">
              <span class="font-bold mb-1 block">From:</span>
              <div>
                <div class="flex gap-1">
                  <span class="font-bold">Excellence Chocolate </span>
                  <p>(David)</p>
                </div>
                <p>
                  Apartment 1204, Maple Heights Residences, 1287 Wellington
                  Street West, Ottawa, Ontario, K1Y 3A7
                </p>
              </div>
            </div>
            <div class="flex-1">
              <span class="font-bold mb-1 block">To:</span>
              <div>
                <div class="flex gap-1">
                  <span class="font-bold">Excellence Chocolate </span>
                  <p>(David)</p>
                </div>
                <p>
                  Apartment 1204, Maple Heights Residences, 1287 Wellington
                  Street West, Ottawa, Ontario, K1Y 3A7
                </p>
              </div>
            </div>
          </div>
          <div class="bg-white">
            <div
              class="flex justify-between items-center border-b border-b-[#E3E5F6] px-4 py-2"
            >
              <span class="flex-1 font-bold">Order Details</span>
              <span class="flex-1 font-bold">Service Details</span>
            </div>
            <!-- Package details -->
            <div>
              <div class="flex gap-4 border-b border-b-[#E3E5F6] px-4 py-2">
                <div class="flex-1">
                  <span class="mb-1 block">Package Details:</span>
                  <p>
                    <span class="font-bold">Description:</span> 12 Skid = TOTAL
                    : 12
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
